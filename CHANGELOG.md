# PACE Changelog

All notable changes to the PACE (Process Automation for Client Engagements) application will be documented in this file.

## [1.21] - 2025-08-30

### Added/Changed
- DFIR email summary robustness; Chubb CS-code summary with CS410/CS120/CS210/CS430 and mandatory $599 fee
- EDR SOC fee handling: applied for monitoring (new/existing console), omitted for hunt-only/client/none
- Always generate MSA; two-party when “No Counsel”, firm-specific/three-party when counsel selected
- Output UX: create and open a client-named subfolder in the selected directory
- Email summary dialog for secondary review is now copy edited text; 
- improved fixed-fee totals (Coalition/Beazley)
- Quiet-by-default logging with PACE_DEBUG override
- Bold formatting of key totals in generated docs
- TACI SOW hours standardized to 45 total

### Fixed
- Correct SOC fee logic for Chubb Existing Console
- Corrected opening of selected output folder to the client-named subfolder
- Corrected label to “Additional agreements (if applicable)”

---

## [1.20] - 2025-08-19

### Added/Changed
- Guided R&R selection inside DFIR (Remote/Onsite + Resources)
- Dropdown/choice-driven section removal in templates (less manual cleanup)
- R<PERSON> SOW auto-generates when R&R is selected in DFIR
- Centralized generation pipeline; improved performance and visibility updates in DFIR UI

### Fixed
- Template path inconsistencies
- R&R template path names
- Error handling and logging improvements
- Version and packaging resources updated for 1.20

---

## [1.1.3] - Previous Release

### Added
- Added mandatory $599.00 fee to all Chubb SOWs (both DFIR and BEC)
- Fixed proper placeholder replacement for chubb_endpoint_count in Chubb SOW templates
- Fixed template path resolution to prioritize templates from the base_templates directory
- Improved error handling in document generation

### Fixed
- Template path resolution issues
- Chubb-specific placeholder handling
- Document generation error handling

---

## [1.1.2] - Previous Release

### Fixed
- Fixed template path resolution in installed version
- Improved HTML entity decoding for special characters
- Fixed issues with TACI and IR SOW documents not opening
- Enhanced error handling and logging for document generation

---

## [1.1.1] - Previous Release

### Fixed
- Fixed Chubb template issues with CS code placeholders
- Improved performance with cleanup of unnecessary files
- Updated documentation and README

---

## [1.1.0] - Previous Release

### Added
- Added DFIR engagement support
- Improved carrier-specific features
- Added Beazley-specific ransomware/network intrusion option
- Enhanced document generation with better error handling
- Improved template handling

---

## [1.0.0] - Initial Release

### Added
- Initial release with basic document generation capabilities
- Support for TACI, BEC, and Recovery & Remediation engagements
- Basic carrier and law firm integration

---

## Performance Metrics (v1.1.4)

| Metric | Before | After | Improvement |
|--------|--------|-------|-------------|
| **Document Generation Functions** | 3 separate | 1 centralized | 67% reduction |
| **Duplicate Import Statements** | 16+ | 0 | 100% elimination |
| **Duplicate Code Blocks** | 7+ | 1 | 86% reduction |
| **Fallback Logic Patterns** | 3 identical | 1 centralized | 67% reduction |
| **Test Import Overhead** | Per-function | Module-level | ~50% faster |

## Migration Notes

### For Users
- **R&R Workflow Change**: Users now need to explicitly check "Generate R&R SOW" to create R&R documents
- **No Breaking Changes**: All existing functionality preserved
- **Better Performance**: Faster startup and document generation

### For Developers
- **Import Changes**: All document generation imports moved to module level
- **New Model Attribute**: `generate_rr_sow` added to `DFIREngagement`
- **Centralized Functions**: Use `_generate_document_with_docxtpl()` for document generation
- **Test Updates**: Test files now use module-level imports for better performance

## Planned Future Enhancements

- **Business Email Compromise (BEC) completion** - Finalize remaining BEC engagement features
- **Threat Actor Communications (TACI) enhancements** - Expand TACI capabilities and workflows
- **Advanced template management** - Improved template organization and customization
- **Enhanced reporting** - Better analytics and engagement tracking
- **Workflow automation** - Additional process automation features
